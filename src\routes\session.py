import traceback

from fastapi import API<PERSON>outer, HTTPException
from uuid import UUID
from typing import List

from sqlalchemy.exc import SQLAlchemyError

from src.services.auth import get_user
from src.utils.logger import AdvancedLogger

from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.Error_Handling import ErrorCode

from src.schemas.assessments import (
    SessionCreate,
    ResponseSubmit,
    SessionResult,
    ResponseRead,
    SessionRead,
)
from src.services.session import (
    create_user_session,
    submit_assessment_responses,
    get_user_sessions,
    get_user_session_by_id,
    get_user_session_profile_by_id,
    get_user_session_response_by_id, delete_user_session, get_user_session_response_profiles,
)
logger = AdvancedLogger(name=__name__)

session_router = APIRouter(prefix="/sessions", tags=["Sessions"])

@session_router.post("/", response_model=SessionRead)
async def start_session(user_id: CurrentUserUpgrade, data: SessionCreate, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)
        return await create_user_session(user.user_id, data, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.get("/", response_model=List[SessionRead])
async def get_all_sessions(user_id: CurrentUserUpgrade, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await get_user_sessions(user.user_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.get("/{session_id}", response_model=SessionRead)
async def get_session(session_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await get_user_session_by_id(user.user_id, session_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.delete("/{session_id}")
async def delete_session(user_id: CurrentUserUpgrade, session_id : UUID, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)
        return await delete_user_session(user.user_id, session_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.post("/{session_id}/submit", response_model=SessionResult)
async def submit_responses(user_id: CurrentUserUpgrade, session_id: UUID, responses: List[ResponseSubmit], db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await submit_assessment_responses(user.user_id, session_id, responses, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.get("/{session_id}/response", response_model=List[ResponseRead])
async def get_session_response(session_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await get_user_session_response_by_id(user.user_id,session_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.get("/{session_id}/profile-result", response_model=SessionResult)
async def get_all_results(user_id: CurrentUserUpgrade, session_id: UUID, db: SessionDep):
    try:
        user = await get_user(db,user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await get_user_session_profile_by_id(session_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

@session_router.get("/profile-results/all", response_model=List[SessionResult])
async def get_all_results(user_id: CurrentUserUpgrade, db: SessionDep):
    try:
        user = await get_user(db, user_id)
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

        return await get_user_session_response_profiles(user.user_id, db)
    except HTTPException as http_exc:
        raise http_exc
    except SQLAlchemyError as db_err:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[DB ERROR] {str(db_err)}")
        raise HTTPException(status_code=500, detail=ErrorCode.ERROR_ON_DATABASE_CONNECTION)
    except Exception as e:
        traceback.print_exc()
        await db.rollback()
        logger.error(f"[UNEXPECTED ERROR] {str(e)}")
        raise HTTPException(status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)


from typing import List
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends

from src.schemas.assessments import AssessmentRead, AssessmentCreate, DimensionRead, AssessmentUpdate, \
    DimensionUpdate, QuestionRead, OptionRead, OptionUpdate, AssessmentNamesRead, ResultProfileRead, \
    ResultProfileCreateList, ResultProfileUpdate, QuestionUpdate
from src.services.assessments import create_assessment, get_assessment_by_id, update_assessment, \
    update_dimension, update_question, update_option, get_questions_by_assessment_id, \
    get_questions_by_dimension_id, get_dimensions_by_assessment_id, get_all_assessments, delete_assessment_by_id, \
    delete_dimension_by_id, delete_option_by_id, delete_question_by_id, get_all_assessments_names, \
    create_assessment_profiles_types, get_assessment_profiles_types, get_assessment_profile_type_by_id, \
    update_assessment_profile_type_by_id, delete_assessment_profile_type_by_id, get_simple_ass_by_id
from src.services.roles import get_contain_manger_user
from src.utils import AdvancedLogger
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.Error_Handling import ErrorCode
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse

assessments_router = APIRouter(
    tags=["Assessments"],
dependencies=[Depends(get_current_user_upgrade)]
)

logger = AdvancedLogger(name=__name__)

# ---------------------------------
# Assessments Routes
# ---------------------------------

# Assessment CRUD operations
@assessments_router.post("/assessments", response_model=AssessmentRead, status_code=200)
@handle_router_exceptions
async def create_assessment_ep(
    data: AssessmentCreate,
    db: SessionDep
):
    assessment = await create_assessment(data, db)
    return AssessmentRead.model_validate(assessment)

@assessments_router.get("/assessments", response_model=List[AssessmentRead])
@handle_router_exceptions
async def get_all_assessments_ep(db: SessionDep):
    assessment = await get_all_assessments(db=db)
    if not assessment:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)
    return [AssessmentRead.model_validate(ass) for ass in assessment]

@assessments_router.get("/assessments/{assessment_id}", response_model=AssessmentRead)
@handle_router_exceptions
async def get_assessment_ep(assessment_id: UUID, db: SessionDep):
    assessment = await get_assessment_by_id(assessment_id=assessment_id, db=db)
    if not assessment:
        raise HTTPException(status_code=404, detail="Assessment not found")
    return AssessmentRead.model_validate(assessment)

@assessments_router.put("/assessments/{assessment_id}", response_model=AssessmentRead)
@handle_router_exceptions
async def update_assessment_ep(user_id: CurrentUserUpgrade, assessment_id: UUID, data: AssessmentUpdate, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)
    updated = await update_assessment(assessment_id, data, db)
    return AssessmentRead.model_validate(updated)

@assessments_router.delete("/assessments/{assessment_id}", status_code=204)
@handle_router_exceptions
async def delete_assessment_ep(user_id: CurrentUserUpgrade,assessment_id: UUID, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    assessment = await delete_assessment_by_id(assessment_id, db)
    if not assessment:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)
    return None

# Assessment names
@assessments_router.get("/assessments-names", response_model=List[AssessmentNamesRead])
@handle_router_exceptions
async def get_assessment_names_ep(db: SessionDep):
    assessments = await get_all_assessments_names(db=db)
    if not assessments:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)
    return [AssessmentNamesRead.model_validate(ass, from_attributes=True) for ass in assessments]

# Assessment profile types
@assessments_router.post('/assessments-types', response_model=List[ResultProfileRead])
@handle_router_exceptions
async def create_assessment_types_ep(user_id: CurrentUserUpgrade, data: ResultProfileCreateList, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    assessment_db = await get_simple_ass_by_id(data.assessment_id, db)
    if not assessment_db:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)
    assessment_profiles = await create_assessment_profiles_types(data=data, db=db)
    if not assessment_profiles:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_TYPES_NOT_CREATED)
    return [ResultProfileRead.model_validate(ass, from_attributes=True) for ass in assessment_profiles]

@assessments_router.get('/assessments-types/{assessment_id}', response_model=List[ResultProfileRead])
@handle_router_exceptions
async def get_assessment_types_ep(assessment_id: UUID, db: SessionDep):
    assessment_profiles = await get_assessment_profiles_types(assessment_id=assessment_id, db=db)
    if not assessment_profiles:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_PROFILES_NOT_FOUND)
    return [ResultProfileRead.model_validate(ass, from_attributes=True) for ass in assessment_profiles]

@assessments_router.get('/assessments-types/{assessment_id}/profile/{profile_id}', response_model=ResultProfileRead)
@handle_router_exceptions
async def get_assessment_type_by_id_ep(profile_id: UUID, assessment_id: UUID, db: SessionDep):
    profile = await get_assessment_profile_type_by_id(profile_id=profile_id, assessment_id=assessment_id, db=db)
    if not profile:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_PROFILE_NOT_FOUND)
    return ResultProfileRead.model_validate(profile, from_attributes=True)

@assessments_router.put('/assessments-types/{assessment_id}/profile/{profile_id}', response_model=ResultProfileRead)
@handle_router_exceptions
async def update_assessment_type_ep(user_id: CurrentUserUpgrade, profile_id: UUID, assessment_id: UUID, data: ResultProfileUpdate, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    updated_profile = await update_assessment_profile_type_by_id(profile_id=profile_id, assessment_id=assessment_id, data=data, db=db)
    if not updated_profile:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_PROFILE_NOT_FOUND)
    return ResultProfileRead.model_validate(updated_profile, from_attributes=True)

@assessments_router.delete('/assessments-types/{assessment_id}/profile/{profile_id}', status_code=204)
@handle_router_exceptions
async def delete_assessment_type_ep(user_id: CurrentUserUpgrade, profile_id: UUID, assessment_id: UUID, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    deleted = await delete_assessment_profile_type_by_id(profile_id=profile_id, assessment_id=assessment_id, db=db)
    if not deleted:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_PROFILE_NOT_FOUND)
    return None

# Assessment Related Resources
@assessments_router.get("/{assessment_id}/dimensions", response_model=List[DimensionRead])
@handle_router_exceptions
async def get_assessment_dimensions_ep(assessment_id: UUID, db: SessionDep):
    dimensions = await get_dimensions_by_assessment_id(assessment_id, db)
    if not dimensions:
        raise HTTPException(status_code=404, detail=ErrorCode.DIMENSION_NOT_FOUND)
    return dimensions

@assessments_router.get("/{assessment_id}/questions", response_model=List[QuestionRead])
@handle_router_exceptions
async def get_assessment_questions_ep(assessment_id: UUID, db: SessionDep):
    questions = await get_questions_by_assessment_id(assessment_id, db)
    if not questions:
        raise HTTPException(status_code=404, detail=ErrorCode.QUESTION_NOT_FOUND)
    return questions

# Dimension Operations
@assessments_router.put("/dimensions/{dimension_id}", response_model=DimensionRead)
@handle_router_exceptions
async def update_dimension_ep(user_id: CurrentUserUpgrade, dimension_id: UUID, data: DimensionUpdate, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    updated = await update_dimension(dimension_id, data, db)
    return DimensionRead.model_validate(updated)

@assessments_router.delete("/dimensions/{dimension_id}", status_code=204)
@handle_router_exceptions
async def delete_dimension_ep(user_id: CurrentUserUpgrade, dimension_id: UUID, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    dimension = await delete_dimension_by_id(dimension_id, db)
    if not dimension:
        raise HTTPException(status_code=404, detail=ErrorCode.DIMENSION_NOT_FOUND)
    return None

@assessments_router.get("/dimensions/{dimension_id}/questions", response_model=List[QuestionRead])
@handle_router_exceptions
async def get_dimension_questions_ep(dimension_id: UUID, db: SessionDep):
    questions = await get_questions_by_dimension_id(dimension_id, db)
    if not questions:
        raise HTTPException(status_code=404, detail=ErrorCode.QUESTION_NOT_FOUND)
    return questions

# Question Operations
@assessments_router.put("/questions/{question_id}", response_model=QuestionRead)
@handle_router_exceptions
async def update_question_ep(user_id: CurrentUserUpgrade, question_id: UUID, data: QuestionUpdate, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    updated = await update_question(question_id, data, db)
    return QuestionRead.model_validate(updated)

@assessments_router.delete("/questions/{question_id}", status_code=204)
@handle_router_exceptions
async def delete_question_ep(user_id: CurrentUserUpgrade, question_id: UUID, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    question = await delete_question_by_id(question_id, db)
    if not question:
        raise HTTPException(status_code=404, detail=ErrorCode.QUESTION_NOT_FOUND)
    return None

# Option Operations
@assessments_router.put("/options/{option_id}", response_model=OptionRead)
@handle_router_exceptions
async def update_option_ep(user_id: CurrentUserUpgrade,option_id: UUID, data: OptionUpdate, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    updated = await update_option(option_id, data, db)
    return OptionRead.model_validate(updated)

@assessments_router.delete("/options/{option_id}", status_code=204)
@handle_router_exceptions
async def delete_option_ep(user_id: CurrentUserUpgrade, option_id: UUID, db: SessionDep):
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)

    option = await delete_option_by_id(option_id, db)
    if not option:
        raise HTTPException(status_code=404, detail=ErrorCode.OPTION_NOT_FOUND)
    return None

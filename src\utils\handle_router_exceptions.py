from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, Request
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from jose import JW<PERSON>rror, ExpiredSignatureError
from pydantic import ValidationError, BaseModel
from functools import wraps
from typing import Callable, Union
from .logger import AdvancedLogger
from config import settings
import traceback

class ErrorResponse(BaseModel):
    is_error: bool
    status_code: int
    detail: Union[str, list]  # Handles both string and validation errors (422)
    class Config:
        use_enum_values = True 


def handle_router_exceptions(func: Callable):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        logger = AdvancedLogger(f"{func.__module__}.{func.__name__}")

        # Try to find request object in args or kwargs
        request = None
        if args and hasattr(args[0], 'method') and hasattr(args[0], 'url'):
            request = args[0]
        elif 'request' in kwargs:
            request = kwargs['request']

        def json_response(status_code: int, message: str | list) -> JSONResponse:
            return JSONResponse(
                status_code=status_code,
                content=ErrorResponse(
                    is_error=True,
                    status_code=status_code,
                    detail=message
                ).model_dump()
            )

        try:
            return await func(*args, **kwargs)

        except (HTTPException, StarletteHTTPException) as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"HTTPException error: {str(e)}")
                traceback.print_exc()
            return json_response(e.status_code, str(e.detail))

        except RequestValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Request validation error: {str(e)}")
                traceback.print_exc()
            return json_response(422, e.errors())

        except ValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Validation error: {str(e)}")
                traceback.print_exc()
            return json_response(422, e.errors())

        except ExpiredSignatureError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Authentication error: {str(e)}")
                traceback.print_exc()
            return json_response(401, "token_has_expired")

        except JWTError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"JWT error: {str(e)}")
                traceback.print_exc()
            return json_response(401, "invalid_token")

        except SQLAlchemyError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Database error: {str(e)}")
                traceback.print_exc()
            return json_response(500, "Database error")

        except TimeoutError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                error_msg = f"Timeout error: {str(e)}"
                if request:
                    error_msg = f"Timeout error for {request.method} {request.url}: {str(e)}"
                logger.error(error_msg)
                traceback.print_exc()
            return json_response(408, "request_timed_out")

        except Exception as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Unexpected error: {str(e)}")
                traceback.print_exc()
            return json_response(500, "Internal_server_error")

    return wrapper


async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            is_error=True,
            status_code=exc.status_code,
            detail=exc.detail
        ).model_dump()
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    missing_fields = []

    for error in exc.errors():
        if error.get("type") == "missing":
            loc = error.get("loc", [])
            # Look for fields missing in the body
            if len(loc) >= 2 and loc[0] == "body":
                missing_fields.append(loc[1])

    if missing_fields:
        message = f"Missing fields: {', '.join(missing_fields)}"
    else:
        message = "Invalid request"

    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            is_error=True,
            status_code=422,
            detail=message
        ).model_dump()
    )
